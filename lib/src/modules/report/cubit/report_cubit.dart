import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_state.dart';
import 'package:koc_app/src/modules/report/data/model/conversion/conversion_summary_response.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/data/repository/report_repository.dart';
import 'package:koc_app/src/modules/shared/mixin/filter_mixin.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

import '../data/model/performance_report_data.dart';

class ReportCubit extends BaseCubit<ReportState> with FilterMixin {
  final ReportRepository reportRepository;

  ReportCubit(this.reportRepository) : super(ReportState());

  Future<void> findReportData() async {
    try {
      DateTimeRange range = getTimeRange(ReportPeriod.LAST_12_MONTHS, null, null);
      int? siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      String? countryCode = await commonCubit.sharedPreferencesService.getCountryCode();

      if (siteId == null) {
        emit(state.copyWith(errorMessage: 'Site ID is not available. Please select a site.'));
        return;
      }

      if (countryCode == null) {
        emit(state.copyWith(errorMessage: 'Country code is not available.'));
        return;
      }

      final findPerformanceChartRequest = FindPerformanceChartRequest(
        siteId: siteId,
        fromDate: range.start,
        toDate: range.end,
        periodBase: ReportQueryPeriodBase.CONVERSION_DATE,
      );

      List<dynamic> results = await Future.wait([
        reportRepository.findPerformanceChart(findPerformanceChartRequest),
        reportRepository.findConversionSummary(siteId),
        reportRepository.findTopTenCampaignsClickCount(
          range.start.toZonedIso8601(countryCode.toCountry),
          range.end.toZonedIso8601(countryCode.toCountry),
          siteId,
        ),
        reportRepository.findPaymentSummary(),
        reportRepository.findMinimumPaymentDetails(),
      ]);
      final performanceChartData = results[0];
      debugPrint('performanceChartData: $performanceChartData');

      List<PerformanceChartData> performanceChartDataList = [];
      if (performanceChartData is List) {
        performanceChartDataList = performanceChartData.map((e) {
          if (e is Map<String, dynamic>) {
            final Map<String, dynamic> convertedData = Map<String, dynamic>.from(e);
            if (convertedData['clicks'] is double) {
              convertedData['clicks'] = (convertedData['clicks'] as double).toInt();
            }
            if (convertedData['conversions'] is double) {
              convertedData['conversions'] = (convertedData['conversions'] as double).toInt();
            }
            return PerformanceChartData.fromJson(convertedData);
          }
          return PerformanceChartData.fromJson(e as Map<String, dynamic>);
        }).toList();
      }
      debugPrint('performanceChartDataList: $performanceChartDataList');

      Map<DateTime, int> currentOneYearClickCount = {};
      Map<DateTime, int> currentOneYearConversionCount = {};

      for (final chartData in performanceChartDataList) {
        try {
          final DateTime parsedDate = DateTime.parse(chartData.date);

          final DateTime monthKey = DateTime(parsedDate.year, parsedDate.month, 1);

          currentOneYearClickCount[monthKey] = (currentOneYearClickCount[monthKey] ?? 0) + chartData.clicks;
          currentOneYearConversionCount[monthKey] =
              (currentOneYearConversionCount[monthKey] ?? 0) + chartData.conversions;
        } catch (e) {
          debugPrint('Error parsing date "${chartData.date}": $e');
          continue;
        }
      }

      debugPrint('Processed ${currentOneYearClickCount.length} months for click data');
      debugPrint('Processed ${currentOneYearConversionCount.length} months for conversion data');
      debugPrint('Monthly click data: $currentOneYearClickCount');
      debugPrint('Monthly conversion data: $currentOneYearConversionCount');

      ConversionSummaryResponse conversionSummaryResponse = results[1];

      emit(state.copyWith(
          currentOneYearClickCount: currentOneYearClickCount,
          currentOneYearConversionCount: currentOneYearConversionCount,
          thisMonthOccurredConversionCount: conversionSummaryResponse.countConversionOccurredThisMonth,
          lastMonthApprovedReward: conversionSummaryResponse.rewardApprovedLastMonth,
          topTenCampaignsClickCount: results[2].data,
          paymentSummary: results[3],
          minimumPaymentDetails: results[4],
          currency: conversionSummaryResponse.currencyCode,
          country: countryCode.toCountry,
          selectedSiteId: siteId));
      debugPrint('paymentSummary: ${results[3]}');
      debugPrint('minimumPaymentDetails: ${results[4]}');
    } on DioException catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes report data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      if (siteId != null) {
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/reports/summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/reports/performance-chart');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/reports/conversion-summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/reports/top-campaigns');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/payment-summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/minimum-payment-details');
        
        await findReportData();
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }
}
