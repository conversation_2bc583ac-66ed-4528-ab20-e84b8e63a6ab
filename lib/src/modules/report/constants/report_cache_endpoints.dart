import 'package:koc_app/src/shared/services/api_service.dart';

/// Cache endpoint constants for report data
///
/// This file contains all the cache endpoints that need to be cleared
/// when refreshing report data, ensuring consistency between different
/// refresh scenarios (pull-to-refresh, site switching, etc.)
class ReportCacheEndpoints {
  /// All cache endpoints that need to be cleared when refreshing report data
  /// These correspond exactly to the API calls made in ReportCubit.findReportData()
  static const List<String> allReportEndpoints = [
    // Performance monthly data endpoint
    '/v3/publishers/me/reports/monthly',

    // Conversion summary endpoint (site-specific)
    '/v3/publishers/me/reports/conversion-summary',

    // Top campaigns chart endpoint (site-specific)
    '/v3/publishers/me/reports/campaign/chart',

    // Payment summary endpoint (user-specific, not site-specific)
    '/v3/publishers/me/payment-summary',

    // Minimum payment details endpoint (country-specific, not site-specific)
    '/v3/publishers/countries/minimum-payment-details',
  ];

  /// Site-specific report endpoints that are affected by site changes
  /// These endpoints include siteId in their parameters and need to be cleared
  /// when switching between sites
  static const List<String> siteSpecificReportEndpoints = [
    '/v3/publishers/me/reports/monthly',
    '/v3/publishers/me/reports/conversion-summary',
    '/v3/publishers/me/reports/campaign/chart',
  ];

  /// Non-site-specific report endpoints that are not affected by site changes
  /// These endpoints don't include siteId in their parameters but are still
  /// part of the report data and should be cleared for complete refresh
  static const List<String> globalReportEndpoints = [
    '/v3/publishers/me/payment-summary',
    '/v3/publishers/countries/minimum-payment-details',
  ];

  /// Helper method to clear all report cache endpoints
  ///
  /// This method provides a reusable way to clear all report-related cache
  /// endpoints, ensuring consistency across different refresh scenarios.
  ///
  /// Usage:
  /// ```dart
  /// final apiService = Modular.get<ApiService>();
  /// await ReportCacheEndpoints.clearAllReportCache(apiService);
  /// ```
  static Future<void> clearAllReportCache(ApiService apiService) async {
    for (final endpoint in allReportEndpoints) {
      await apiService.clearCacheForEndpoint(endpoint);
    }
  }

  /// Helper method to clear only site-specific report cache endpoints
  ///
  /// This method clears only the endpoints that are affected by site changes.
  /// Useful when you want to refresh only site-specific data without clearing
  /// global data like payment summaries.
  ///
  /// Usage:
  /// ```dart
  /// final apiService = Modular.get<ApiService>();
  /// await ReportCacheEndpoints.clearSiteSpecificReportCache(apiService);
  /// ```
  static Future<void> clearSiteSpecificReportCache(ApiService apiService) async {
    for (final endpoint in siteSpecificReportEndpoints) {
      await apiService.clearCacheForEndpoint(endpoint);
    }
  }
}
