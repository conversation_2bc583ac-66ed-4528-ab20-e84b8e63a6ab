import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/data/repository/campaign_repository.dart';
import 'package:koc_app/src/modules/home/<USER>/home_state.dart';
import 'package:koc_app/src/modules/home/<USER>/data/repository/voucher_repository.dart';

import '../../../shared/services/shared_preferences_service.dart';
import '../../../shared/utils/handle_error.dart';
import '../../notification/data/model/notification_data.dart';
import '../../notification/data/repository/notification_repository.dart';
import '../../report/data/model/performance_report_data.dart';
import '../../report/data/model/report_model.dart';
import '../../report/data/repository/report_repository.dart';
import '../voucher/data/model/voucher.dart';

class HomeCubit extends BaseCubit<HomeState> {
  static final String? superPointPageLink = dotenv.env['SUPER_POINT_PAGE_LINK'];
  final SharedPreferencesService _sharedPreferencesService;
  final VoucherRepository _voucherRepository;
  final CampaignRepository _campaignRepository;
  final NotificationRepository _notificationRepository;
  final ReportRepository _reportRepository;
  final AccountRepository _accountRepository;
  String currency = "";

  HomeCubit(this._sharedPreferencesService, this._voucherRepository, this._campaignRepository,
      this._notificationRepository, this._reportRepository, this._accountRepository)
      : super(HomeState());

  Future<void> initState() async {
    try {
      currency = await _sharedPreferencesService.getPublisherCurrency() ?? "";
      final futures = [
        _findNotifications(),
        _findLastSevenDayPerformance(),
        _findPerformanceChart(),
        _findVouchers(),
        _findCampaigns(),
        _findSsoKey(),
        _sharedPreferencesService.getCountryCode(),
      ];
      final results = await Future.wait(futures);
      String ssoKey = results[5] as String;
      String countryCode = results[6] as String;
      emit(state.copyWith(
          homeCarousel: results[0] as List<HomeCarousel>,
          lastSevenDayPerformance: results[1] as ReportSummary,
          clicks: (results[2] as List<PerformanceChartData>).map((e) => e.clicks).toList(),
          conversions: (results[2] as List<PerformanceChartData>).map((e) => e.conversions).toList(),
          vouchers: results[3] as List<Voucher>,
          promotedCampaigns: results[4] as List<DefaultCampaignSummary>,
          academyLink: 'https://academy.accesstrade.global/autologin.php?secret=$ssoKey&redirect=courses',
          inpageLink: 'https://inpage.es/autologin.php?secret=$ssoKey',
          superPointLink: '$superPointPageLink$ssoKey&country=$countryCode'));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<String> _findSsoKey() async {
    try {
      return await _accountRepository.findSsoKey();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return "";
    }
  }

  Future<List<HomeCarousel>> _findNotifications() async {
    try {
      final result = await _notificationRepository.findNotifications(style: NotificationStyle.CAROUSEL.name);
      return result is List ? result.map((item) => HomeCarousel.fromJson(item)).toList() : [];
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return [];
    }
  }

  Future<ReportSummary> _findLastSevenDayPerformance() async {
    try {
      final now = DateTime.now();
      final startDate = now.subtract(const Duration(days: 7)).copyWith(hour: 0, minute: 0, second: 0);
      final endDate = now.copyWith(hour: 23, minute: 59, second: 59);
      final compareStartDate = now.subtract(const Duration(days: 14)).copyWith(hour: 0, minute: 0, second: 0);
      final compareEndDate = startDate.copyWith(hour: 23, minute: 59, second: 59);
      final request = FindReportSummaryRequest(
        fromDate: startDate,
        toDate: endDate,
        compareFromDate: compareStartDate,
        compareToDate: compareEndDate,
      );
      final result = await _reportRepository.findReportSummary(request);
      return result is Map<String, dynamic> ? ReportSummary.fromJson(result) : const ReportSummary();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return const ReportSummary();
    }
  }

  Future<List<PerformanceChartData>> _findPerformanceChart() async {
    try {
      final siteId = await _sharedPreferencesService.getCurrentSiteId();
      final fromDate = DateTime.now().subtract(const Duration(days: 6)).copyWith(hour: 0, minute: 0, second: 0);
      final toDate = DateTime.now().copyWith(hour: 23, minute: 59, second: 59);
      final request = FindPerformanceChartRequest(
        siteId: siteId!,
        fromDate: fromDate,
        toDate: toDate,
        periodBase: ReportQueryPeriodBase.CONVERSION_DATE,
      );
      final result = await _reportRepository.findPerformanceChart(request);
      return result is List ? result.map((item) => PerformanceChartData.fromJson(item)).toList() : [];
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return [];
    }
  }

  Future<List<Voucher>> _findVouchers() async {
    try {
      final siteId = await _sharedPreferencesService.getCurrentSiteId();
      List<Voucher> vouchers = await _voucherRepository.findVouchers(FindVouchersRequest(siteId: siteId!));
      return vouchers;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return [];
    }
  }

  Future<List<DefaultCampaignSummary>> _findCampaigns() async {
    try {
      final siteId = await _sharedPreferencesService.getCurrentSiteId();
      final result = await _campaignRepository.findCampaignFeatureSummary(siteId!);
      return result is List ? result.map((item) => DefaultCampaignSummary.fromJson(item)).toList() : [];
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return [];
    }
  }

  Future<void> toggleChart() async {
    emit(state.copyWith(showChart: !state.showChart));
  }

  /// Pull-to-refresh method that clears cache and fetches fresh data
  Future<void> pullToRefresh() async {
    try {
      final apiService = Modular.get<ApiService>();
      final siteId = await _sharedPreferencesService.getCurrentSiteId();

      if (siteId != null) {
        await apiService.clearCacheForEndpoint('/v3/publishers/me/notifications');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/daily');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/vouchers');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/featured-summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sso-key');
      }

      await initState();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
