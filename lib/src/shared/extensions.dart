import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';

import 'constants/dio_constants.dart';

extension StringExtension on String {
  String toTitleCase() {
    if (isEmpty) return this;

    bool isAllUpperCase = this == toUpperCase();

    if (isAllUpperCase) {
      return "${this[0]}${substring(1).toLowerCase()}";
    }

    return replaceAllMapped(RegExp(r'[A-Z]'), (Match match) => ' ${match.group(0)}').trim().split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }

  String toSecure() {
    if (length > 4) {
      String maskedPart = '*' * (length - 4);
      String visiblePart = substring(length - 4);
      String secureResult = maskedPart + visiblePart;
      if (secureResult.length > 10) {
        return secureResult.substring(secureResult.length - 10);
      }
      return secureResult;
    }
    return this;
  }

  String get currencySymbol => NumberFormat().simpleCurrencySymbol(this);
  DateTime toDateTime(String pattern) => DateFormat(pattern).parse(this);
  Country get toCountry => Country.values.firstWhere((e) => e.code == this);
}

extension NumExtensions on num {
  String toPercentage() {
    return '${NumberFormat('#,###.##').format(this * 100)}%';
  }

  String toPercentageChanged() {
    if (this == 0) {
      return '';
    }
    return '${NumberFormat('#,###.##').format(this * 100)}%';
  }

  String toCommaSeparated() {
    final formatter = NumberFormat('#,###.##');
    return formatter.format(this);
  }

  String toPrice(String currencyCode) {
    final formatter = NumberFormat('#,###.##');
    return '${formatter.simpleCurrencySymbol(currencyCode)}${formatter.format(this)}';
  }

  String toReward(RewardType type) {
    if (type == RewardType.CPA_SALES) {
      return '${this * 100}%';
    }
    return toCommaSeparated();
  }

  String toCompactFormat() {
    final absValue = abs();

    if (absValue < 1000) {
      return toInt().toString();
    } else if (absValue < 1000000) {
      final value = absValue / 1000;
      if (value % 1 == 0) {
        return '${value.toInt()}k';
      } else {
        return '${value.toStringAsFixed(1)}k';
      }
    } else if (absValue < 1000000000) {
      final value = absValue / 1000000;
      if (value % 1 == 0) {
        return '${value.toInt()}M';
      } else {
        return '${value.toStringAsFixed(1)}M';
      }
    } else {
      final value = absValue / 1000000000;
      if (value % 1 == 0) {
        return '${value.toInt()}B';
      } else {
        return '${value.toStringAsFixed(1)}B';
      }
    }
  }
}

extension DateTimeExtensions on DateTime {
  static var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  String toFormattedString() {
    return '${months[month - 1]} $day, $year';
  }

  String toMonthAndYear() {
    return '${months[month - 1]} $year';
  }

  String toMonthAndDay() {
    return '${months[month - 1]} ${day.toString().padLeft(2, '0')}';
  }

  String toZonedIso8601(Country country) {
    return toIso8601String().split('.').first + country.offset;
  }

  String toStandard() {
    return DateFormat(abbreviateMonthDayYearTimeFormat).format(this);
  }

  String toDateMonthYear() {
    return DateFormat(dateMonthYearFormat).format(this);
  }

  String toFullDateTime() {
    return DateFormat(fullDateTimeFormat).format(this);
  }

  String toYearMonthDay() {
    return DateFormat('yyyy-MM-dd').format(this);
  }

  String toYearMonth() {
    return DateFormat(yearMonthFormat).format(this);
  }

  String toHowManyBefore() {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inMinutes == 0) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} mins ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hour ago';
    } else {
      return '${difference.inDays} day ago';
    }
  }
}

extension SnackBarExtension on BuildContext {
  void clearSnackBars() {
    ScaffoldMessenger.of(this).clearSnackBars();
  }

  void showSnackBar(String content,
      {String title = '', int durationSecond = 3, String? actionTitle, VoidCallback? actionTap}) {
    ScaffoldMessenger.of(this).clearSnackBars();
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title.isNotEmpty)
                    Text(title,
                        style: Theme.of(this)
                            .textTheme
                            .labelMedium!
                            .copyWith(color: Colors.white, fontWeight: FontWeight.bold)),
                  Text(
                    content,
                    style: Theme.of(this)
                        .textTheme
                        .labelMedium!
                        .copyWith(color: Colors.white, fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ),
            if (actionTitle != null)
              TextButton(
                onPressed: actionTap,
                child: Text(
                  actionTitle,
                  style: Theme.of(this)
                      .textTheme
                      .labelMedium!
                      .copyWith(color: ColorConstants.textButtonColor, fontWeight: FontWeight.w500),
                ),
              ),
            GestureDetector(
              onTap: () {
                ScaffoldMessenger.of(this).hideCurrentSnackBar();
              },
              child: Icon(
                Icons.close,
                size: 20.r,
                color: Colors.white,
              ),
            )
          ],
        ),
        behavior: SnackBarBehavior.floating,
        duration: durationSecond != 0 ? Duration(seconds: durationSecond) : const Duration(hours: 1),
      ),
    );
  }
}

extension TextThemeException on BuildContext {
  TextStyle textTitleLarge({FontWeight? fontWeight, Color? color, double? fontSize}) =>
      Theme.of(this).textTheme.titleLarge!.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize);
  TextStyle textTitleMedium({FontWeight? fontWeight, Color? color, double? fontSize}) =>
      Theme.of(this).textTheme.titleMedium!.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize);
  TextStyle textTitleSmall({FontWeight? fontWeight, Color? color, double? fontSize}) =>
      Theme.of(this).textTheme.titleSmall!.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize);
  TextStyle textBodyLarge({FontWeight? fontWeight, Color? color, double? fontSize}) =>
      Theme.of(this).textTheme.bodyLarge!.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize);
  TextStyle textBodyMedium({FontWeight? fontWeight, Color? color, double? fontSize}) =>
      Theme.of(this).textTheme.bodyMedium!.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize);
  TextStyle textBodySmall({FontWeight? fontWeight, Color? color, double? fontSize}) =>
      Theme.of(this).textTheme.bodySmall!.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize);
  TextStyle textLabelLarge({FontWeight? fontWeight, Color? color, double? fontSize}) =>
      Theme.of(this).textTheme.labelLarge!.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize);
  TextStyle textLabelMedium({FontWeight? fontWeight, Color? color, double? fontSize}) =>
      Theme.of(this).textTheme.labelMedium!.copyWith(fontWeight: fontWeight, color: color, fontSize: fontSize);
}

extension DataSourceExtension on DataSource {
  get navigatorKey => null;

  Failure getFailure() {
    switch (this) {
      case DataSource.success:
        return Failure(ResponseCode.success, ResponseMessage.success.tr());
      case DataSource.noContent:
        return Failure(ResponseCode.noContent, ResponseMessage.noContent.tr());
      case DataSource.badRequest:
        return Failure(ResponseCode.badRequest, ResponseMessage.badRequest.tr());
      case DataSource.forbidden:
        return Failure(ResponseCode.forbidden, ResponseMessage.forbidden.tr());
      case DataSource.unauthorised:
        return Failure(ResponseCode.unauthorised, ResponseMessage.unauthorised.tr());
      case DataSource.notFound:
        return Failure(ResponseCode.notFound, ResponseMessage.notFound.tr());
      case DataSource.internetServerError:
        return Failure(ResponseCode.internalServerError, ResponseMessage.internalServerError.tr());
      case DataSource.connectTimeout:
        return Failure(ResponseCode.connectTimeout, ResponseMessage.connectTimeout.tr());
      case DataSource.connectionError:
        return Failure(ResponseCode.connectionError, ResponseMessage.connectionError.tr());
      case DataSource.cancel:
        return Failure(ResponseCode.cancel, ResponseMessage.cancel.tr());
      case DataSource.receiveTimeout:
        return Failure(ResponseCode.receiveTimeout, ResponseMessage.receiveTimeout.tr());
      case DataSource.sendTimeout:
        return Failure(ResponseCode.sendTimeout, ResponseMessage.sendTimeout.tr());
      case DataSource.cacheError:
        return Failure(ResponseCode.cacheError, ResponseMessage.cacheError.tr());
      case DataSource.noInternetConnection:
        return Failure(ResponseCode.noInternetConnection, ResponseMessage.noInternetConnection.tr());
      case DataSource.defaultError:
        return Failure(ResponseCode.defaultError, ResponseMessage.defaultError.tr());
    }
  }
}

class Failure {
  final int code;
  final String message;

  Failure(this.code, this.message);
}
